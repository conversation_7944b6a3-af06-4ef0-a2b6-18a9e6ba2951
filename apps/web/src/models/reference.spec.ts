import { describe, expect, it } from 'vitest';
import { PostalCodeSchema, TheftReportSchema } from './reference';

describe('PostalCodeSchema', () => {
  it('空文字', () => expect(PostalCodeSchema.parse('')).toBe(null));
  it('123-1234', () => expect(PostalCodeSchema.parse('123-1234')).toBe('123-1234'));
  it('1231234', () => expect(PostalCodeSchema.parse('1231234')).toBe('123-1234'));
});
describe('TheftReportSchema', () => {
  it('有', () => expect(TheftReportSchema.parse('有')).toBe(true));
  it('有り', () => expect(TheftReportSchema.parse('有り')).toBe(true));
  it('あり', () => expect(TheftReportSchema.parse('あり')).toBe(true));
  it('無', () => expect(TheftReportSchema.parse('無')).toBe(false));
  it('無し', () => expect(TheftReportSchema.parse('無し')).toBe(false));
  it('なし', () => expect(TheftReportSchema.parse('なし')).toBe(false));
  it('空文字', () => expect(TheftReportSchema.parse('')).toBe(false));
});
