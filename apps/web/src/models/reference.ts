import { format, isValid } from 'date-fns';
import <PERSON> from 'papa<PERSON><PERSON>';
import { P, match } from 'ts-pattern';
import { z } from 'zod';

import { ResponseStatusSchema, fileToText, filenameSuffix, isNullish, throwError } from 'common';
import type { PoliceReference } from 'lambda-api';

import { downloadTextAsShiftJis } from '@/funcs/file';

const tableHeader = [
  'No',
  '整理番号',
  '照会内容',
  '氏名',
  '郵便番号',
  '住所',
  '盗難届',
  '届出日',
  '受理番号',
] as const;
type PoliceReferenceRow = Record<(typeof tableHeader)[number], string>;

const createRequestFileName = (r: PoliceReference) => {
  if (r.requestedAt === null) throw new Error('respondedAt is null');
  return `${r.prefecture}警察照会依頼(${format(new Date(r.requestedAt), filenameSuffix)})`;
};

const createResponseFileName = (r: PoliceReference) => {
  if (r.respondedAt === null) throw new Error('respondedAt is null');
  return `${r.prefecture}警察照会回答(${format(new Date(r.respondedAt), filenameSuffix)})`;
};

export const downloadPoliceRequestCsv = (reference: PoliceReference) => {
  const { bicycles } = reference;
  const rows = [
    tableHeader,
    ...bicycles.map(({ request: { no, serialNo, registrationNumber, serialNumber } }) => {
      const referenceBy = match({ registrationNumber, serialNumber })
        .with({ registrationNumber: P.string }, () => `防犯登録番号: ${registrationNumber}`)
        .with({ serialNumber: P.string }, () => `車体番号: ${serialNumber}`)
        .with({ registrationNumber: null, serialNumber: null }, () => throwError())
        .exhaustive();
      return [
        no,
        serialNo,
        referenceBy,
        '', // name,
        '', // postalCode,
        '', // address,
        '', // theftReport,
        '', // theftReportedAt,
        '', // receiptNumber,
      ];
    }),
  ];
  const csv = Papa.unparse(rows, { header: false, quotes: true });
  const filename = `${createRequestFileName(reference)}.csv`;
  downloadTextAsShiftJis(filename, csv);
};

export const downloadPoliceRequestExcel = async (reference: PoliceReference) => {
  const s = (() => {
    const BORDER_STYLE = 'thin';
    const COLOR_SPEC = { rgb: '00000000' };
    return {
      border: {
        top: { style: BORDER_STYLE, color: COLOR_SPEC },
        bottom: { style: BORDER_STYLE, color: COLOR_SPEC },
        left: { style: BORDER_STYLE, color: COLOR_SPEC },
        right: { style: BORDER_STYLE, color: COLOR_SPEC },
      },
      alignment: { vertical: 'top', wrapText: true },
    };
  })();

  const { bicycles } = reference;

  const rows = [
    tableHeader.map((v) => ({ t: 's', v, s })),
    ...bicycles.map(({ policeReferenceBy, request: { no, serialNo } }) => {
      if (isNullish(policeReferenceBy)) throw new Error('policeReferenceBy is nullish');
      const { type, value } = policeReferenceBy;
      const referenceBy = match(type)
        .with('registrationNumber', () => `防犯登録番号: ${value}`)
        .with('serialNumber', () => `車体番号: ${value}`)
        .exhaustive();
      const row = [
        { t: 'n', v: no },
        { t: 's', v: serialNo },
        { t: 's', v: referenceBy },
        { t: 's', v: '' }, // name,
        { t: 's', v: '' }, // postalCode,
        { t: 's', v: '' }, // address,
        { t: 's', v: '' }, // theftReport,
        { t: 's', v: '' }, // theftReportedAt,
        { t: 's', v: '' }, // receiptNumber,
      ] as const satisfies { t: 's' | 'n'; v: string | number }[];
      return row.map((cell) => ({ ...cell, s }));
    }),
  ];
  const XLSX = await import('xlsx');
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(rows);
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  const filename = `${createRequestFileName(reference)}.xlsx`;
  XLSX.writeFile(wb, filename);
};

export const downloadPoliceResponseCsv = (reference: PoliceReference) => {
  if (reference.respondedAt === null) throw new Error('respondedAt is null');
  const { bicycles } = reference;
  const rows = [
    tableHeader,
    ...bicycles.map(({ request: { no, serialNo, registrationNumber, serialNumber }, response }) => {
      const referenceBy = match({ registrationNumber, serialNumber })
        .with({ registrationNumber: P.string }, () => `防犯登録番号: ${registrationNumber}`)
        .with({ serialNumber: P.string }, () => `車体番号: ${serialNumber}`)
        .with({ registrationNumber: null, serialNumber: null }, () => throwError())
        .exhaustive();
      const theftReport = match(response?.theftReport)
        .with(true, () => '有')
        .with(false, () => '無')
        .with(undefined, () => '-')
        .with(null, () => '-')
        .exhaustive();
      return [
        no,
        serialNo,
        referenceBy,
        response?.name ?? '',
        response?.postalCode ?? '',
        response?.address ?? '',
        theftReport,
        response?.theftReportedAt ?? '',
        response?.receiptNumber ?? '',
      ];
    }),
  ];

  const csv = Papa.unparse(rows, { header: false, quotes: true });
  const filename = `${createResponseFileName(reference)}.csv`;
  downloadTextAsShiftJis(filename, csv);
};

export const downloadPoliceResponseExcel = async (reference: PoliceReference) => {
  if (reference.respondedAt === null) throw new Error('respondedAt is null');
  const s = (() => {
    const BORDER_STYLE = 'thin';
    const COLOR_SPEC = { rgb: '00000000' };
    return {
      border: {
        top: { style: BORDER_STYLE, color: COLOR_SPEC },
        bottom: { style: BORDER_STYLE, color: COLOR_SPEC },
        left: { style: BORDER_STYLE, color: COLOR_SPEC },
        right: { style: BORDER_STYLE, color: COLOR_SPEC },
      },
      alignment: { vertical: 'top', wrapText: true },
    };
  })();

  const { bicycles } = reference;

  const rows = [
    tableHeader.map((v) => ({ t: 's', v, s })),
    ...bicycles.map(({ policeReferenceBy, request: { no, serialNo }, response }) => {
      if (isNullish(policeReferenceBy)) throw new Error('policeReferenceBy is nullish');
      const { type, value } = policeReferenceBy;
      const referenceBy = match(type)
        .with('registrationNumber', () => `防犯登録番号: ${value}`)
        .with('serialNumber', () => `車体番号: ${value}`)
        .exhaustive();
      const theftReport = match(response?.theftReport)
        .with(true, () => '有')
        .with(false, () => '無')
        .with(undefined, () => '-')
        .with(null, () => '-')
        .exhaustive();
      const row = [
        { t: 'n', v: no },
        { t: 's', v: serialNo },
        { t: 's', v: referenceBy },
        { t: 's', v: response?.name ?? '' },
        { t: 's', v: response?.postalCode ?? '' },
        { t: 's', v: response?.address ?? '' },
        { t: 's', v: theftReport },
        { t: 's', v: response?.theftReportedAt ?? '' },
        { t: 's', v: response?.receiptNumber ?? '' },
      ] as const satisfies { t: 's' | 'n'; v: string | number }[];
      return row.map((cell) => ({ ...cell, s }));
    }),
  ];
  const XLSX = await import('xlsx');
  const wb = XLSX.utils.book_new();
  const ws = XLSX.utils.aoa_to_sheet(rows);
  XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
  const filename = `${createResponseFileName(reference)}.xlsx`;
  XLSX.writeFile(wb, filename);
};

const NoSchema = z.coerce.number().int().nonnegative();
const SerialNoSchema = z.string().min(1, '必須です');
const ResponseBySchema = z.string();
const NameSchema = z.string().transform((v) => (v.length !== 0 ? v : null));
export const PostalCodeSchema = z
  .string()
  .regex(/|\d{3}-?\d{4}/, 'フォーマットが不正です')
  .transform((v) => {
    if (v.length === 0) return null; // ハイフンあり
    if (v.length === 8) return v; // ハイフンあり
    return `${v.slice(0, 3)}-${v.slice(3)}`; // ハイフン付与
  });
const AddressSchema = z.string().transform((v) => (v.length !== 0 ? v : null));
export const TheftReportSchema = z.string().transform((v) => {
  console.log('TheftReportSchema transform:', { input: v, type: typeof v, length: v.length });
  if (/有|あり/g.test(v)) {
    console.log('TheftReportSchema: matched 有|あり, returning true');
    return true;
  }
  if (v === '') {
    console.log('TheftReportSchema: empty string, returning false');
    return false;
  }
  const result = !/無|なし/g.test(v);
  console.log('TheftReportSchema: default case, returning', result);
  return result;
});
const TheftReportedAtSchema = z
  .string()
  .refine((v) => {
    if (v === '') return true;
    const date = new Date(v);
    return isValid(date);
  })
  .transform((v) => (v !== '' ? format(new Date(v), 'yyyy-MM-dd') : null));
const ReceiptNumberSchema = z.string().transform((v) => (v.length !== 0 ? v : null));

if (import.meta.vitest) {
  const { describe, it, expect } = import.meta.vitest;
  describe('PostalCodeSchema', () => {
    it('空文字', () => expect(PostalCodeSchema.parse('')).toBe(null));
    it('123-1234', () => expect(PostalCodeSchema.parse('123-1234')).toBe('123-1234'));
    it('1231234', () => expect(PostalCodeSchema.parse('1231234')).toBe('123-1234'));
  });
  describe('TheftReportSchema', () => {
    it('有', () => expect(TheftReportSchema.parse('有')).toBe(true));
    it('有り', () => expect(TheftReportSchema.parse('有り')).toBe(true));
    it('あり', () => expect(TheftReportSchema.parse('あり')).toBe(true));
    it('無', () => expect(TheftReportSchema.parse('無')).toBe(false));
    it('無し', () => expect(TheftReportSchema.parse('無し')).toBe(false));
    it('なし', () => expect(TheftReportSchema.parse('なし')).toBe(false));
    it('空文字', () => expect(TheftReportSchema.parse('')).toBe(false));
  });
}

export type PoliceReferenceResponseItem = {
  id: string;
  status: z.SafeParseReturnType<'success' | 'notFound', 'success' | 'notFound'>;
  no: z.SafeParseReturnType<number, number>;
  serialNo: z.SafeParseReturnType<string, string>;
  responseBy: z.SafeParseReturnType<string, string>;
  name: z.SafeParseReturnType<string, string | null>;
  postalCode: z.SafeParseReturnType<string, string | null>;
  address: z.SafeParseReturnType<string, string | null>;
  theftReport: z.SafeParseReturnType<string, boolean>;
  theftReportedAt: z.SafeParseReturnType<string, string | null>;
  receiptNumber: z.SafeParseReturnType<string, string | null>;
};

const rowToItem = (row: PoliceReferenceRow): PoliceReferenceResponseItem => {
  // Debug logging for theftReport field
  console.log('rowToItem debug:', {
    theftReportValue: row.盗難届,
    theftReportType: typeof row.盗難届,
    allRowKeys: Object.keys(row),
    fullRow: row,
  });

  return {
    id: crypto.randomUUID(),
    status: ResponseStatusSchema.safeParse(row.氏名.length !== 0 ? 'success' : 'notFound'),
    no: NoSchema.safeParse(row.No),
    serialNo: SerialNoSchema.safeParse(row.整理番号),
    responseBy: ResponseBySchema.safeParse(row.照会内容),
    name: NameSchema.safeParse(row.氏名 ?? ''),
    postalCode: PostalCodeSchema.safeParse(row.郵便番号 ?? ''),
    address: AddressSchema.safeParse(row.住所 ?? ''),
    theftReport: TheftReportSchema.safeParse(row.盗難届 ?? ''),
    theftReportedAt: TheftReportedAtSchema.safeParse(row.届出日 ?? ''),
    receiptNumber: ReceiptNumberSchema.safeParse(row.受理番号 ?? ''),
  };
};

export const parseResponseExcel = async (file: File): Promise<PoliceReferenceResponseItem[]> => {
  const XLSX = await import('xlsx');
  const workbook = XLSX.read(await file.arrayBuffer(), { type: 'array' });
  const sheet = workbook.Sheets.Sheet1;
  // TODO: 無視する行の設定を考慮した処理を追加する
  // https://docs.sheetjs.com/docs/api/utilities/array#array-output
  const rows = XLSX.utils.sheet_to_json<PoliceReferenceRow>(sheet);
  return rows.map(rowToItem);
};

export const parseResponseCsv = async (file: File): Promise<PoliceReferenceResponseItem[]> => {
  const csv = await fileToText(file, 'sjis');
  // TODO: 無視する行の設定を考慮した処理を追加する
  const rows = Papa.parse<PoliceReferenceRow>(csv, { header: true, skipEmptyLines: true }).data;
  return rows.map(rowToItem);
};

// https://developer.mozilla.org/ja/docs/Web/HTTP/Guides/MIME_types/Common_types
const csvFileType = 'text/csv';
const excelFileType = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';

export const parseResponseFile = async (file: File): Promise<PoliceReferenceResponseItem[]> => {
  if (file.type === csvFileType) return parseResponseCsv(file);
  if (file.type === excelFileType) return parseResponseExcel(file);
  throw new Error('不正なファイル形式です');
};
