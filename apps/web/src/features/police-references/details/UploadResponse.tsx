import WarningIcon from '@mui/icons-material/Warning';
import {
  <PERSON>,
  Button,
  DialogContent,
  DialogTitle,
  FormHelperText,
  Stack,
  Typography,
} from '@mui/material';
import React from 'react';

import type { PoliceReference, PostalAddress } from 'lambda-api';
import { Csv, DataGrid } from 'mui-ex';

import { trpc } from '@/api';
import { VisuallyHiddenInput } from '@/components/VisuallyHiddenInput';
import { imagesCol, removedAtCol, statusCol } from '@/components/bicycles/columns';
import { type ColDefFactory, createUncheckedUseColumns } from '@/hooks/useColumns';
import { type PoliceReferenceResponseItem, parseResponseFile } from '@/models/reference';
import { addressToPostalCodes } from '@/utils/address';
import { DateTimePicker } from '@mui/x-date-pickers';
import { useQueryClient } from '@tanstack/react-query';
import { getQueryKey } from '@trpc/react-query';
import type { Prefecture } from 'common';
import { extractPrefecture } from 'models';
import type { StrictExclude } from 'ts-essentials';
import { match } from 'ts-pattern';
import { z } from 'zod';
import { referenceByCol } from '../columns';
import { PostalCodeSelectionDialog } from './PostalCodeSelectionDialog';
import type { DuplicatedPostalCode, SelectedPostalCode } from './types';

type StateItem = PoliceReference['bicycles'][number] & { item?: PoliceReferenceResponseItem };
type State = StateItem[];

const valueGetter = (
  bicycle: StateItem,
  key: StrictExclude<keyof PoliceReferenceResponseItem, 'id'>,
) => {
  if (bicycle.item === undefined) return '-';
  const result = bicycle.item[key];
  return result.success ? result.data : result.error.errors.at(0)?.message;
};

type RenderCell = (
  key: StrictExclude<keyof PoliceReferenceResponseItem, 'id'>,
) => NonNullable<ReturnType<ColDefFactory<StateItem>>['renderCell']>;
const renderCell: RenderCell =
  (key) =>
  ({ row }) => {
    const error = Boolean(row.item?.[key]?.error);
    const value = row.item === undefined ? '-' : valueGetter(row, key);
    if (!error) return value;
    return (
      <Typography component="span" color="error" sx={{ fontSize: (t) => t.typography.body2 }}>
        {value}
      </Typography>
    );
  };

const responseStatusCol: ColDefFactory<StateItem> = ({ fit }) => ({
  field: 'item.status',
  headerName: '回答ステータス',
  width: fit('回答ステータス'),
  valueGetter: (_, params) => {
    if (params.item === undefined) return '-';
    if (!params.item.status.success) throw new Error('status is not success');
    return params.item.status.data === 'success' ? '✅ 回答有り' : '❌ 回答無し';
  },
});

const serialTagCol: ColDefFactory<StateItem> = ({ fit }) => ({
  field: 'item.serialNo',
  headerName: '整理番号',
  width: fit('整理番号が一致するデータがありません'),
  renderCell: ({ row }) => {
    const error = !row.item || Boolean(row.item.serialNo.error);
    const value =
      row.item === undefined
        ? '整理番号が一致するデータがありません'
        : valueGetter(row, 'serialNo');
    if (!error) return value;
    return (
      <Typography component="span" color="error" sx={{ fontSize: (t) => t.typography.body2 }}>
        {value}
      </Typography>
    );
  },
});

const nameCol: ColDefFactory<StateItem> = ({ fit }) => ({
  field: 'item.name',
  headerName: '氏名',
  width: fit('●●●●＠●●●●＠'),
  renderCell: renderCell('name'),
});

const postalCodeCol: ColDefFactory<StateItem> = ({ fit }) => ({
  field: 'item.postalCode',
  headerName: '郵便番号',
  width: fit('郵便番号が見つかりませんでした'),
  renderCell: ({ row }) => {
    const error = Boolean(row.item?.postalCode.error);
    const value = row.item === undefined ? '-' : valueGetter(row, 'postalCode');

    const addressExists = row.item?.address.success && row.item.address.data;
    const postalCodeEmpty = !row.item?.postalCode.success || !row.item.postalCode.data;
    const showWarning = addressExists && postalCodeEmpty;

    if (error) {
      return (
        <Typography component="span" color="error" sx={{ fontSize: (t) => t.typography.body2 }}>
          {value}
        </Typography>
      );
    }

    if (showWarning) {
      return (
        <Typography
          component="span"
          color="warning"
          sx={{
            fontSize: (t) => t.typography.body2,
            display: 'flex',
            alignItems: 'center',
            width: 1,
            height: 1,
          }}
        >
          <WarningIcon fontSize="small" sx={{ mr: 0.5 }} />
          郵便番号が見つかりませんでした
        </Typography>
      );
    }

    return value;
  },
});

const addressCol: ColDefFactory<StateItem> = () => ({
  field: 'item.address',
  headerName: '住所',
  width: 200,
  renderCell: renderCell('address'),
});

const theftReportCol: ColDefFactory<StateItem> = () => ({
  field: 'item.theftReport',
  headerName: '盗難届',
  type: 'boolean',
  width: 100,
  valueGetter: (_, params) => {
    if (params.item === undefined) return null;
    if (!params.item.theftReport.success) return null;
    return params.item.theftReport.data;
  },
  renderCell: ({ row }) => {
    const error = Boolean(row.item?.theftReport.error);
    if (error) {
      const value = row.item === undefined ? '-' : valueGetter(row, 'theftReport');
      return (
        <Typography component="span" color="error" sx={{ fontSize: (t) => t.typography.body2 }}>
          {value}
        </Typography>
      );
    }
    // For successful boolean values, let the DataGrid handle the default boolean rendering
    // by returning undefined, which tells the DataGrid to use its default boolean cell renderer
    return undefined;
  },
});

const theftReportedAtCol: ColDefFactory<StateItem> = ({ fit }) => ({
  field: 'item.theftReportedAt',
  headerName: '届出日',
  width: fit('yyyy-mm-dd'),
  renderCell: renderCell('theftReportedAt'),
});

const receiptNumberCol: ColDefFactory<StateItem> = () => ({
  field: 'item.receiptNumber',
  headerName: '受理番号',
  width: 200,
  renderCell: renderCell('receiptNumber'),
});

const useColumns = createUncheckedUseColumns([
  imagesCol,
  responseStatusCol,
  removedAtCol,
  statusCol,
  serialTagCol,
  referenceByCol,
  nameCol,
  postalCodeCol,
  addressCol,
  theftReportCol,
  theftReportedAtCol,
  receiptNumberCol,
]);

const isValidItem = ({ item }: StateItem) =>
  item && Object.values(item).every((v) => typeof v === 'string' || v.success);

const requireSelectPostalCode = (item: PoliceReferenceResponseItem): boolean => {
  if (item.postalCode.data !== null) return false;
  return item.address.data?.length !== 0;
};

const fillPostalCodes = (
  items: PoliceReferenceResponseItem[],
  postalData: PostalAddress[],
): {
  items: PoliceReferenceResponseItem[];
  duplicated: DuplicatedPostalCode[];
} => {
  const duplicated: DuplicatedPostalCode[] = [];
  const updatedItems = items.map((item) => {
    if (!requireSelectPostalCode(item)) return item;

    const address = item.address.data;
    if (!address) return item;

    const prefecture = extractPrefecture(address.replace(/\s+/g, ''));
    console.log('prefecture', prefecture);
    if (prefecture === undefined) return item;

    const postalCodes = addressToPostalCodes(address, prefecture, postalData);
    console.log('postalCodes', postalCodes);

    if (postalCodes.length === 0) return item;

    if (postalCodes.length === 1)
      return { ...item, postalCode: z.string().safeParse(postalCodes[0].code) };

    duplicated.push({
      id: item.id,
      address,
      options: postalCodes.map((p) => ({
        code: p.code,
        townAreaRaw: p.townAreaRaw ?? p.townArea,
      })),
    });
    return item;
  });
  return { items: updatedItems, duplicated };
};

type Props = {
  reference: PoliceReference;
  onClose: () => void;
  prefecture: Prefecture;
};

export const UploadResponse = ({ reference, onClose, prefecture }: Props) => {
  const [respondedAt, setRespondedAt] = React.useState<Date | null>(new Date());
  const [items, setItems] = React.useState<State>([]);
  const [parsedItems, setParsedItems] = React.useState<PoliceReferenceResponseItem[]>([]);
  const [duplicated, setDuplicated] = React.useState<DuplicatedPostalCode[]>([]);
  const columns = useColumns(items);

  const ref = React.useRef<HTMLInputElement | null>(null);
  const handleClick = () => ref.current?.click();

  const { data: postalData = [] } = trpc.address.list.useQuery({ prefecture });
  const mergeItemIntoBicycle = (items: PoliceReferenceResponseItem[]) => {
    const merged = reference.bicycles.map((bicycle) => {
      const serialNo = bicycle.request.serialNo;
      const item = items.find((item) => item.serialNo.success && item.serialNo.data === serialNo);
      return { ...bicycle, item };
    });
    setItems(merged);
  };

  const handleChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { files } = e.target;
    if (files === null || files.length === 0) return;
    const file = files[0];
    const parsed = await parseResponseFile(file);
    console.log('parsed', parsed);
    setParsedItems(parsed);

    const result = fillPostalCodes(parsed, postalData);
    console.log('result', result);
    mergeItemIntoBicycle(result.items);
    setDuplicated(result.duplicated);
    // 同じファイルを再度選択できるようにファイル入力値をリセットする
    if (ref.current) ref.current.value = '';
  };

  const handleSelectPostalCodes = async (selected: SelectedPostalCode[]) => {
    const updatedItems = parsedItems.map((item) => {
      const found = selected.find((s) => s.id === item.id);
      if (found === undefined || found.code === null) return item;
      const postalCode = z.string().safeParse(found.code);
      return { ...item, postalCode };
    });
    setParsedItems(updatedItems);
    mergeItemIntoBicycle(updatedItems);
  };

  const handleDialogClose = () => setDuplicated([]);

  const queryClient = useQueryClient();
  const queryKey = getQueryKey(trpc.policeReferences.get, { id: reference.id });
  const mutation = trpc.policeReferences.receiveResponse.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey });
      onClose();
    },
  });

  const handleUpdate = () => {
    if (respondedAt === null) throw new Error('respondedAt is null');
    mutation.mutate({
      id: reference.id,
      respondedAt,
      responses: items.filter(isValidItem).map(({ item }) => {
        if (item === undefined) throw new Error('item is undefined');
        if (!item.serialNo.success) throw new Error('serialNo is not success');
        if (!item.name.success) throw new Error('name is not success');
        if (!item.postalCode.success) throw new Error('postalCode is not success');
        if (!item.address.success) throw new Error('address is not success');
        if (!item.theftReport.success) throw new Error('theftReport is not success');
        if (!item.theftReportedAt.success) throw new Error('theftReportedAt is not success');
        if (!item.receiptNumber.success) throw new Error('receiptNumber is not success');

        // 名前の有無で回答があるかどうかを判定
        if (item.name.data === null)
          return {
            status: 'notFound',
            serialNo: item.serialNo.data,
            name: null,
            postalCode: null,
            address: null,
            theftReport: null,
            theftReportedAt: null,
            receiptNumber: null,
          };

        return {
          status: 'success',
          serialNo: item.serialNo.data,
          name: item.name.data,
          postalCode: item.postalCode.data,
          address: item.address.data,
          theftReport: item.theftReport.data,
          theftReportedAt: item.theftReportedAt.data,
          receiptNumber: item.receiptNumber.data,
        };
      }),
    });
  };

  const noMatch = items.every((item) => item.item === undefined);
  const noFilled = items.every((item) => !isValidItem(item));
  const notAny = items.some((item) => !isValidItem(item));
  const isError = respondedAt === null || noMatch || noFilled;
  const message = match({ respondedAt, noMatch, noFilled, notAny })
    .with({ respondedAt: null }, () => '回答日を入力してください')
    .with({ noMatch: true }, () => '整理番号が一致するデータがありません')
    .with({ noFilled: true }, () => 'アップロード可能な回答データがありません')
    .with({ notAny: true }, () => (
      <>
        回答されていないデータ、または不正なデータがあります。
        <br />
        このままアップロードする場合、それらのデータは警察照会をキャンセルしたものとして処理されます。
      </>
    ))
    .otherwise(() => undefined);
  return (
    <>
      <DialogTitle>回答ファイルのアップロード</DialogTitle>
      <DialogContent>
        <Stack spacing={2}>
          <Stack direction={{ xs: 'column', sm: 'row' }} alignItems="flex-end" spacing={2}>
            <DateTimePicker
              label="回答日時"
              value={respondedAt}
              onChange={(value) => setRespondedAt(value)}
              sx={{ width: 240 }}
            />
            <Box>
              <Button variant="contained" endIcon={<Csv />} onClick={handleClick}>
                回答ファイルを選択する
              </Button>
            </Box>
            <VisuallyHiddenInput
              ref={ref}
              type="file"
              accept=".csv, .xlsx"
              onChange={handleChange}
            />
          </Stack>
          <Stack sx={{ width: 1, flexGrow: 1, overflow: 'auto' }}>
            <DataGrid
              columns={columns}
              rows={items}
              disableRowSelectionOnClick
              hideFooterSelectedRowCount
              sx={{
                bgcolor: (t) => t.palette.background.paper,
                // https://mui.com/x/react-data-grid/style-recipes/#remove-cell-focus-outline
                '& .MuiDataGrid-cell:focus-within': { outline: 'none' },
                '& .MuiTablePagination-select': { mr: 2 },
                '& .MuiDataGrid-row:hover': { backgroundColor: 'inherit' },
              }}
            />
          </Stack>
        </Stack>
      </DialogContent>
      <Stack alignItems="end" sx={{ px: 3, pb: 1 }}>
        <Stack direction="row" spacing={1}>
          <Button variant="outlined" onClick={onClose}>
            キャンセル
          </Button>
          <Button variant="contained" onClick={handleUpdate} disabled={isError}>
            アップロード
          </Button>
        </Stack>
        {items.length !== 0 && <FormHelperText error={isError}>{message}</FormHelperText>}
      </Stack>
      <PostalCodeSelectionDialog
        open={duplicated.length > 0}
        onClose={handleDialogClose}
        onComplete={handleSelectPostalCodes}
        duplicated={duplicated}
      />
    </>
  );
};
